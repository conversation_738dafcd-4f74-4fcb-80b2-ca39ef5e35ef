<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <!--purchase order line-->
    <record
        id="purchase_order_view_form_inherit_double_uom_purchase"
        model="ir.ui.view"
    >
        <field name="name">purchase.order.view.form.inherit</field>
        <field name="model">purchase.order</field>
        <field name="type">form</field>
        <field name="inherit_id" ref="purchase.purchase_order_form" />
        <field name="arch" type="xml">
            <xpath
                expr="//field[@name='order_line']/list//field[@name='price_unit']"
                position="before"
            >
<!--                <field name="has_different_uom_categ" column_invisible="1" />-->
                <field
                    name="coefficient"
                    force_save="1"
                    invisible="double_uom_id == False"
                    optional="show"
                />
                <field
                    name="qty_double_uom"
                    optional="show"
                    invisible="double_uom_id == False"/>
<!--                    readonly="has_different_uom_categ == False or edit_qty"-->
                <field
                    name="double_uom_id"
                    force_save="1"
                    readonly="1"
                    optional="show"
                />
                <field
                    name="qdu_received"
                    column_invisible="parent.state not in ['purchase', 'done']"
                    invisible="double_uom_id == False"
                    class="text-info"
                    optional="show"
                />
<!--                <field-->
<!--                    name="qdu_invoiced"-->
<!--                    column_invisible="parent.state not in ['purchase', 'done']"-->
<!--                    invisible="double_uom_id == False"-->
<!--                    class="text-info"-->
<!--                    optional="show"-->
<!--                />-->
<!--                <field name="is_product_double_uom" column_invisible="1" />-->
            </xpath>

            <xpath
                expr="//field[@name='order_line']/list/field[@name='product_qty']"
                position="before"
            >
                <field
                    name="edit_qty"
                    optional="show"
                    invisible="double_uom_id == False"
                    nolabel="1"
                />
                <!--                    invisible="double_uom_id == False or has_different_uom_categ == False"-->

            </xpath>

            <xpath
                expr="//field[@name='order_line']/list//field[@name='product_qty']"
                position="attributes"
            >
                <attribute
                    name="readonly"
                >edit_qty == False</attribute>
                <attribute name="force_save">1</attribute>
<!--                <attribute-->
<!--                    name="readonly"-->
<!--                >has_different_uom_categ == True and edit_qty == False</attribute>-->
<!--                <attribute name="force_save">1</attribute>-->
            </xpath>

            <xpath
                expr="//field[@name='order_line']/list//field[@name='product_uom'][1]"
                position="attributes"
            >
                <attribute
                    name="readonly"
                >not edit_qty</attribute>
<!--                <attribute-->
<!--                    name="readonly"-->
<!--                >is_product_double_uom and not edit_qty</attribute>-->
            </xpath>
            <xpath
                expr="//field[@name='order_line']/list//field[@name='product_uom'][2]"
                position="attributes"
            >
                <attribute
                    name="readonly"
                >not edit_qty</attribute>
<!--                <attribute-->
<!--                    name="readonly"-->
<!--                >is_product_double_uom and not edit_qty</attribute>-->
            </xpath>
        </field>
    </record>
</odoo>
