import logging

from odoo import api, fields, models

_logger = logging.getLogger(__name__)


class AccountMoveLine(models.Model):
    _inherit = "account.move.line"

    qty_double_uom = fields.Float(
        string="Q.D.U",
        help="Quantity for double unit of measure",
        digits="Double Unit of Measure",
    )
    double_uom_id = fields.Many2one(
        "uom.uom",
        string="D.U",
        help="Double unit of measure",
        compute="_compute_du_coefficient",
        precompute=True,
        store=True,
    )
    coefficient = fields.Float(
        help="This coefficient is used to convert double units into a single "
        "unit of measurement.",
        digits="Double Unit of Measure",
        compute="_compute_du_coefficient",
        precompute=True,
        store=True,
    )
    has_different_uom_categ = fields.<PERSON><PERSON><PERSON>(
        compute="_compute_has_different_uom_categ", store=True, precompute=True
    )

    # -------------------------------------------------------------------------
    # COMPUTE METHODS
    # -------------------------------------------------------------------------

    @api.depends("product_id")
    def _compute_quantity(self):
        """
        Override method of odoo to:
        Set the coefficient of the product's double UOM as the suggested
        quantity when selecting a product in the account move line"""
        for record in self:
            if record.display_type == "product":
                if not record.quantity and record.product_id:
                    if record.product_id.is_double_uom and (
                        record.move_type in ["out_invoice", "out_refund"]
                        or (
                            (
                                record.product_id.double_uom_id.category_id
                                != record.product_uom_id.category_id
                            )
                            and record.move_type in ["in_refund", "in_invoice"]
                        )
                    ):
                        record.quantity = record.coefficient
                    else:
                        record.quantity = 1
            else:
                record.quantity = False

    @api.depends(
        "double_uom_id.category_id",
        "product_uom_id.category_id",
        "product_id.is_double_uom",
    )
    def _compute_has_different_uom_categ(self):
        for record in self:
            if record.product_id.is_double_uom:
                record.has_different_uom_categ = bool(
                    record.double_uom_id.category_id
                    != record.product_uom_id.category_id
                )
            else:
                record.has_different_uom_categ = False

    @api.depends("product_id")
    def _compute_du_coefficient(self):
        for record in self:
            if record.product_id.is_double_uom and record.move_type in [
                "in_refund",
                "in_invoice",
                "out_invoice",
                "out_refund",
            ]:
                if record.move_type in ["out_invoice", "out_refund"] or (
                    (
                        record.product_id.double_uom_id.category_id
                        != record.product_uom_id.category_id
                    )
                    and record.move_type in ["in_refund", "in_invoice"]
                ):
                    record.double_uom_id = record.product_id.double_uom_id
                else:
                    record.double_uom_id = record.product_id.uom_id.id
                record.coefficient = record.product_id.coefficient
            else:
                record.coefficient = 0
                record.double_uom_id = False

    @api.depends("product_id")
    def _compute_product_uom_id(self):
        res = super()._compute_product_uom_id()
        # Fill the field uom with the default purchase unit
        # if it is a supplier invoice or refund.
        for record in self:
            if record.move_type in ["in_refund", "in_invoice"]:
                record.product_uom_id = record.product_id.uom_po_id
            else:
                record.product_uom_id = record.product_id.uom_id
        return res

    # -------------------------------------------------------------------------
    # ONCHANGE METHODS
    # -------------------------------------------------------------------------

    @api.onchange("qty_double_uom")
    def _onchange_qty_double_uom(self):
        if self.has_different_uom_categ:
            self.quantity = self.qty_double_uom * self.coefficient

    @api.onchange("quantity", "product_id")
    def _onchange_quantity(self):
        if (
            self.product_id
            and self.product_id.is_double_uom
            and self.move_type
            in ["in_refund", "in_invoice", "out_invoice", "out_refund"]
        ):
            if self.move_type in ["in_refund", "in_invoice"]:
                self.qty_double_uom = (
                    self.quantity / self.coefficient
                    if self.double_uom_id.category_id != self.product_uom_id.category_id
                    else self.quantity * self.coefficient
                )
            else:
                self.qty_double_uom = self.quantity / self.coefficient
        else:
            self.qty_double_uom = 0
