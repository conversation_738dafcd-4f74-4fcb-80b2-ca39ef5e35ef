from odoo.addons.double_uom_purchase.tests.common import DoubleUomPurchaseCommon


class TestSaleOrderLine(DoubleUomPurchaseCommon):
    def test_inverse_qty_double_uom(self):
        """Test the inverse method for qty_double_uom."""
        self.order_line.qty_double_uom = 8.0
        self.order_line._inverse_qty_double_uom()
        self.assertEqual(self.order_line.product_qty, 16.0)

    def test_suggest_quantity(self):
        """Test the _suggest_quantity method."""
        self.order_line._suggest_quantity()
        self.assertEqual(self.order_line.product_qty, 2.0)

    def test_prepare_account_move_line(self):
        """Test the _prepare_account_move_line method."""
        self.order_line.product_qty = 10
        move_line_vals = self.order_line._prepare_account_move_line()
        self.assertEqual(
            move_line_vals["qty_double_uom"],
            self.order_line.qdu_to_invoice,
            "qty_double_uom should match qdu_to_invoice",
        )
        self.assertEqual(
            move_line_vals["double_uom_id"],
            self.product_double_uom.id,
            "Double UOM ID should match the product's double UOM",
        )

    def test_get_qdu_procurement(self):
        """Test the _get_qdu_procurement method."""
        self.assertEqual(
            self.order_line._get_qdu_procurement(),
            0.0,
            "Initial QDU procurement should be zero without stock moves",
        )

    def test_prepare_stock_moves(self):
        """Test the _prepare_stock_moves method."""
        picking = self.env["stock.picking"].create(
            {
                "partner_id": self.purchase_order.partner_id.id,
                "picking_type_id": self.env.ref("stock.picking_type_in").id,
            }
        )
        stock_moves = self.order_line._prepare_stock_moves(picking)
        self.assertTrue(stock_moves, "Stock moves should be prepared correctly")
        self.assertEqual(
            stock_moves[0]["qdu_done"],
            5,
            "QDU Done should be initialized to 0 in prepared stock moves",
        )
