<?xml version="1.0" ?>
<odoo>
    <template
        id="report_purchaseorder_document_inherit_double_uom_purchase"
        inherit_id="purchase.report_purchaseorder_document"
    >
        <xpath expr="//thead//th[@name='th_quantity']" position="after">
            <t
                t-set="display_du"
                t-value="o.order_line.filtered(lambda l: l.product_id.is_double_uom)"
            />
            <t t-if="display_du">
                <th name="th_qty_double_uom" class="text-center">Q.D.U</th>
            </t>
        </xpath>
        <xpath expr="//span[@t-field='line.product_qty']/.." position="after">
            <t t-if="display_du">
                <td name="td_qty_double_uom" class="text-end">
                    <span t-field="line.qty_double_uom" />
                    <span t-field="line.double_uom_id" />
                </td>
            </t>
        </xpath>
    </template>

    <template
        id="report_purchasequotation_document_inherit_double_uom_purchase"
        inherit_id="purchase.report_purchasequotation_document"
    >
        <xpath expr="//thead//th[@name='th_quantity']" position="after">
            <t
                t-set="display_du"
                t-value="o.order_line.filtered(lambda l: l.product_id.is_double_uom)"
            />
            <t t-if="display_du">
                <th name="th_qty_double_uom" class="text-center">Q.D.U</th>
            </t>
        </xpath>
        <xpath
            expr="//span[@t-field='order_line.product_qty']/.."
            position="after"
        >
            <t t-if="display_du">
                <td name="td_qty_double_uom" class="text-center">
                    <span t-field="order_line.qty_double_uom" />
                    <span t-field="order_line.double_uom_id" />
                </td>
            </t>
        </xpath>
    </template>
</odoo>
