# Double UoM Account

This module extends Odoo's accounting functionalities by incorporating support
for a double unit of measure (UoM) system, allowing for more granular control
and flexibility in accounting operations.

**Table of contents**

- [Overview](#overview)
- [Configuration](#configuration)
- [Maintainer](#maintainer)

## Overview

The Double UoM Account module enhances the accounting system by introducing a
"double unit of measure". Key features include:

- A new field qty_double_uom to manage quantities of double unit of measure.
- Automatic updates and computations for double UoM quantities based on the type
  of transaction (e.g., invoices, refunds).
- Computation of coefficients to convert between standard and double UoM,
  accommodating differences in categories between the two units.

![margin policy](static/img/invoice_du.png)

This module is particularly useful for businesses dealing with complex units of
measurement or requiring multi-dimensional tracking for their inventory and
accounting operations.

## Configuration

To configure the module:

- Go to Settings > Accounting.
- Enable the "QDU Tax" feature if needed by checking the corresponding option.
- Ensure that your products have the necessary configurations for double units
  of measure.

![margin policy](static/img/qdu_tax.png)

## Maintainer

This module is maintained by the OKTEO (https://www.okteo.fr/).

![icon](static/description/icon.png)
