# Double Unit of Measure for Purchase Orders

## Overview

The **Double UOM for Purchase Orders** module enhances the Purchase Order
process by allowing the use of two different units of measure (UOM) for
products. This is particularly useful for businesses that need to manage
purchases or inventory in multiple measurement systems, such as handling
products in both **pieces** and **boxes**, or **kilograms** and **packs**.

## Functional Features

### 1. **Dual UOM Support**

- The module allows products to be tracked using both a **primary unit of
  measure** and a **secondary unit of measure**.
- It is useful for companies that need to handle items that can be purchased or
  sold in different units of measurement, e.g., buying products in pieces but
  receiving or shipping them in boxes.

### 2. **Unit of Measure Conversion**

- The module supports an **automatic conversion** between the primary and
  secondary units of measure based on a predefined conversion factor.
- This ensures that when quantities are updated in one unit of measure, the
  corresponding value in the other unit is automatically calculated, providing
  accurate inventory management and order processing.

### 3. **Conditional Visibility and Behavior**

- Depending on whether a product requires dual UOM, the system dynamically
  adjusts which fields are visible and editable in the Purchase Order.
- Fields related to the secondary unit of measure will only appear when
  applicable, improving the clarity of the user interface.

### 4. **Enhanced Purchase Order Lines**

- Users can specify quantities in both units of measure for products that
  require dual UOM.
- The system allows for easy tracking of products in both measurement systems,
  while ensuring that the information remains organized and clear in the
  Purchase Order lines.

![icon](static/img/purchase_order.png)

![icon](static/img/receipt.png)

### 5. **Reporting Enhancements**

- Reports for Purchase Orders and Purchase Quotations are enhanced to include
  columns for the secondary unit of measure.
- This provides users with the ability to view quantities in both the primary
  and secondary units of measure in reports, allowing for easier tracking and
  reconciliation.

![icon](static/img/report_purchase_order.png)

![icon](static/img/report_request_quotation.png)

### 6. **Automatic Updates**

- Any changes made to the quantity in the primary unit of measure are
  automatically reflected in the secondary unit of measure.
- This feature eliminates the need for manual calculations and ensures accurate
  tracking of quantities throughout the order and invoicing process.

## Installation

1. Install the module through the Odoo Apps menu.
2. Ensure the product UOM configurations are set up to support both primary and
   secondary units of measure.
3. Configure your products to specify the UOM conversion ratio.

## Maintainer

This module is maintained by the OKTEO (https://www.okteo.fr/).

![icon](static/description/icon.png)
