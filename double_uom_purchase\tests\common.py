from odoo.tests.common import TransactionCase


class DoubleUomPurchaseCommon(TransactionCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()

        cls.ProductProduct = cls.env["product.product"]
        cls.ResPartner = cls.env["res.partner"]
        cls.Purchase = cls.env["purchase.order"]
        cls.PurchaseLine = cls.env["purchase.order.line"]

        # Model Data
        cls.product_uom_kg = cls.env.ref("uom.product_uom_kgm")
        cls.product_double_uom = cls.env.ref("uom.product_uom_unit")

        # Partner
        cls.partner = cls.ResPartner.create(
            {
                "name": "Test Partner",
            }
        )

        # DU Product and Simple Product
        cls.du_product = cls.ProductProduct.create(
            {
                "name": "DU",
                "is_double_uom": True,
                "type": "consu",
                "is_storable": True,
                "invoice_policy": "delivery",
                "tracking": "lot",
                "coefficient": 2,
                "uom_id": cls.product_uom_kg.id,
                "double_uom_id": cls.product_double_uom.id,
                "uom_po_id": cls.product_uom_kg.id,
            }
        )

        cls.purchase_order = cls.Purchase.create(
            {
                "partner_id": cls.partner.id,
            }
        )

        cls.order_line = cls.PurchaseLine.create(
            {
                "order_id": cls.purchase_order.id,
                "product_id": cls.du_product.id,
                "product_qty": 10,
                "coefficient": 2,
                "product_uom": cls.product_uom_kg.id,
            }
        )
