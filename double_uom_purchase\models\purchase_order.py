from odoo import models
from odoo.tools.float_utils import float_compare


class PurchaseOrder(models.Model):
    _inherit = "purchase.order"

    # -------------------------------------------------------------------------
    # ORM METHODS
    # -------------------------------------------------------------------------

    def write(self, vals):
        if vals.get("order_line") and self.state == "purchase":
            for order in self:
                pre_order_line_qty = {
                    order_line: {
                        "product_qty": order_line.product_qty,
                        "qty_double_uom": order_line.qty_double_uom,
                    }
                    for order_line in order.mapped("order_line")
                }
        res = super().write(vals)
        if vals.get("order_line") and self.state == "purchase":
            for order in self:
                to_log = {}
                for order_line in order.order_line:
                    if (
                        pre_order_line_qty.get(order_line, False)
                        and float_compare(
                            pre_order_line_qty[order_line]["product_qty"],
                            order_line.product_qty,
                            precision_rounding=order_line.product_uom.rounding,
                        )
                        > 0
                        and float_compare(
                            pre_order_line_qty[order_line]["qty_double_uom"],
                            order_line.qty_double_uom,
                            precision_rounding=order_line.product_uom.rounding,
                        )
                        > 0
                    ):
                        to_log[order_line] = (
                            order_line.product_qty,
                            pre_order_line_qty[order_line]["product_qty"],
                            order_line.qty_double_uom,
                            pre_order_line_qty[order_line]["qty_double_uom"],
                        )
                if to_log:
                    order._log_decrease_ordered_quantity(to_log)
        return res
