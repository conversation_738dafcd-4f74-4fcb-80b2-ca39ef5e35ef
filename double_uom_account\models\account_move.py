from odoo import Command, models


class AccountMove(models.Model):
    _inherit = "account.move"

    # -------------------------------------------------------------------------
    # ACTION METHODS
    # -------------------------------------------------------------------------

    def action_switch_move_type(self):
        """
        This method checks the total amount for each move. If the amount is negative,
        it updates the `qty_double_uom` field to its negative value for all lines
        where the `display_type` is 'product'"""
        for move in self:
            if move.amount_total < 0:
                move.write(
                    {
                        "line_ids": [
                            Command.update(
                                line.id, {"qty_double_uom": -line.qty_double_uom}
                            )
                            for line in move.line_ids
                            if line.display_type == "product"
                        ]
                    }
                )
        return super().action_switch_move_type()
