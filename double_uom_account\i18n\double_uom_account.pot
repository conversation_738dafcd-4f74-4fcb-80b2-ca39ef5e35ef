# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* double_uom_account
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-25 15:32+0000\n"
"PO-Revision-Date: 2024-12-25 15:32+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: double_uom_account
#: model_terms:ir.ui.view,arch_db:double_uom_account.res_config_settings_view_form_inherit_double_uom_account
msgid "Calculate fixed tax based on Q.D.U"
msgstr ""

#. module: double_uom_account
#: model:ir.model.fields,field_description:double_uom_account.field_account_move_line__coefficient
msgid "Coefficient"
msgstr ""

#. module: double_uom_account
#: model:ir.model,name:double_uom_account.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: double_uom_account
#: model:ir.model.fields,field_description:double_uom_account.field_account_move_line__double_uom_id
msgid "D.U"
msgstr ""

#. module: double_uom_account
#: model:ir.model.fields,help:double_uom_account.field_account_move_line__double_uom_id
msgid "Double unit of measure"
msgstr ""

#. module: double_uom_account
#: model:ir.model.fields,field_description:double_uom_account.field_account_move_line__has_different_uom_categ
msgid "Has Different Uom Categ"
msgstr ""

#. module: double_uom_account
#: model:ir.model,name:double_uom_account.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: double_uom_account
#: model:ir.model,name:double_uom_account.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: double_uom_account
#: model:ir.model.fields,field_description:double_uom_account.field_account_move_line__qty_double_uom
#: model_terms:ir.ui.view,arch_db:double_uom_account.report_invoice_document_inherit_double_uom_account
msgid "Q.D.U"
msgstr ""

#. module: double_uom_account
#: model:ir.model.fields,field_description:double_uom_account.field_res_config_settings__module_double_uom_account_tax
#: model_terms:ir.ui.view,arch_db:double_uom_account.res_config_settings_view_form_inherit_double_uom_account
msgid "QDU Tax"
msgstr ""

#. module: double_uom_account
#: model:ir.model.fields,help:double_uom_account.field_account_move_line__qty_double_uom
msgid "Quantity for double unit of measure"
msgstr ""

#. module: double_uom_account
#: model:ir.model.fields,help:double_uom_account.field_account_move_line__coefficient
msgid ""
"This coefficient is used to convert double units into a single unit of "
"measurement."
msgstr ""
