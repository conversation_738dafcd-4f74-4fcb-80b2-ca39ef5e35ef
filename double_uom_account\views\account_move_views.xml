<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record
        id="account_move_view_form_inherit_double_uom_account"
        model="ir.ui.view"
    >
        <field name="name">account.move.view.form.inherit</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form" />
        <field name="arch" type="xml">
            <xpath
                expr="//field[@name='invoice_line_ids']//list//field[@name='price_unit']"
                position="before"
            >
                <field name="has_different_uom_categ" column_invisible="1" />
                <field
                    name="coefficient"
                    invisible="double_uom_id == False"
                    optional="hide"
                />
                <field
                    name="qty_double_uom"
                    invisible="double_uom_id == False"
                    readonly="has_different_uom_categ == False"
                    force_save="1"
                    optional="hide"
                />
                <field name="double_uom_id" optional="hide" />
            </xpath>
            <xpath
                expr="//field[@name='invoice_line_ids']//form//field[@name='price_unit']"
                position="after"
            >
                <field name="coefficient" invisible="double_uom_id == False" />
                <field
                    name="qty_double_uom"
                    invisible="double_uom_id == False"
                />
                <field name="double_uom_id" />
            </xpath>
            <xpath
                expr="//field[@name='invoice_line_ids']/list//field[@name='quantity']"
                position="attributes"
            >
                <attribute
                    name="readonly"
                >has_different_uom_categ == True</attribute>
                <attribute name="force_save">1</attribute>
            </xpath>
            <xpath
                expr="//field[@name='invoice_line_ids']/list//field[@name='product_uom_id']"
                position="attributes"
            >
                <attribute name="readonly">double_uom_id != False</attribute>
            </xpath>
        </field>
    </record>
</odoo>
