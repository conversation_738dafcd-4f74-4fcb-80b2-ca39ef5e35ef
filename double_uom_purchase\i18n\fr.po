# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* double_uom_purchase
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-05 09:13+0000\n"
"PO-Revision-Date: 2024-12-05 09:13+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: double_uom_purchase
#: model:ir.model.fields,field_description:double_uom_purchase.field_purchase_order_line__coefficient
msgid "Coefficient"
msgstr ""

#. module: double_uom_purchase
#: model:ir.model.fields,field_description:double_uom_purchase.field_purchase_order_line__double_uom_id
msgid "D.U"
msgstr ""

#. module: double_uom_purchase
#: model:ir.model.fields,help:double_uom_purchase.field_purchase_order_line__double_uom_id
msgid "Double unit of measure"
msgstr "Double unité de mesure"

#. module: double_uom_purchase
#: model:ir.model.fields,field_description:double_uom_purchase.field_purchase_order_line__edit_qty
msgid "Edit QTY"
msgstr "Modifier la quantité"

#. module: double_uom_purchase
#: model:ir.model.fields,help:double_uom_purchase.field_purchase_order_line__edit_qty
msgid ""
"Enable to manually edit the Q.D.U. If disabled, the quantity is "
"automatically calculated based on the coefficient and the product's unit of "
"measure settings."
msgstr ""
"Activez pour modifier manuellement la Q.D.U. Si désactivé, la quantité est automatiquement calculée "
"en fonction du coefficient et des paramètres d'unité de mesure du produit."

#. module: double_uom_purchase
#: model:ir.model.fields,field_description:double_uom_purchase.field_purchase_order_line__has_different_uom_categ
msgid "Has Different Uom Categ"
msgstr ""

#. module: double_uom_purchase
#: model:ir.model.fields,field_description:double_uom_purchase.field_purchase_order_line__is_product_double_uom
msgid "Is Product Double Uom"
msgstr ""

#. module: double_uom_purchase
#: model:ir.model,name:double_uom_purchase.model_purchase_order
msgid "Purchase Order"
msgstr "Bon de commande fournisseur"

#. module: double_uom_purchase
#: model:ir.model,name:double_uom_purchase.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Ligne de bon de commande"

#. module: double_uom_purchase
#: model:ir.model.fields,field_description:double_uom_purchase.field_purchase_order_line__qty_double_uom
#: model_terms:ir.ui.view,arch_db:double_uom_purchase.report_purchaseorder_document_inherit_double_uom_purchase
#: model_terms:ir.ui.view,arch_db:double_uom_purchase.report_purchasequotation_document_inherit_double_uom_purchase
msgid "Q.D.U"
msgstr ""

#. module: double_uom_purchase
#: model:ir.model.fields,field_description:double_uom_purchase.field_purchase_order_line__qdu_invoiced
msgid "Q.D.U Invoiced"
msgstr "Q.D.U Facturée"

#. module: double_uom_purchase
#: model:ir.model.fields,field_description:double_uom_purchase.field_purchase_order_line__qdu_received
msgid "Q.D.U Received"
msgstr "Q.D.U Reçu"

#. module: double_uom_purchase
#: model:ir.model.fields,field_description:double_uom_purchase.field_purchase_order_line__qdu_to_invoice
msgid "Q.D.U To Invoice"
msgstr "Q.D.U à facturer"

#. module: double_uom_purchase
#: model:ir.model.fields,help:double_uom_purchase.field_purchase_order_line__qty_double_uom
msgid "Quantity for double unit of measure"
msgstr "Quantité pour la double unité de mesure"

#. module: double_uom_purchase
#: model:ir.model.fields,help:double_uom_purchase.field_purchase_order_line__qdu_invoiced
msgid "Quantity invoiced for double unit of measure"
msgstr "Quantité facturée pour la double unité de mesure"

#. module: double_uom_purchase
#: model:ir.model.fields,help:double_uom_purchase.field_purchase_order_line__qdu_received
msgid "Quantity received for double unit of measure"
msgstr "Quantité reçue pour la double unité de mesure"

#. module: double_uom_purchase
#: model:ir.model.fields,help:double_uom_purchase.field_purchase_order_line__qdu_to_invoice
msgid "Quantity to invoice for double unit of measure"
msgstr "Quantité à facturer pour la double unité de mesure"

#. module: double_uom_purchase
#: model:ir.model.fields,help:double_uom_purchase.field_purchase_order_line__coefficient
msgid ""
"This coefficient is used to convert double units into a single unit of "
"measurement."
msgstr ""
"Ce coefficient est utilisé pour convertir des unités doubles en une unité de"
" mesure"
