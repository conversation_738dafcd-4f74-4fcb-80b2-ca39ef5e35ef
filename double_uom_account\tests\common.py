from odoo.fields import Command
from odoo.tests.common import TransactionCase


class DoubleUomAccountCommon(TransactionCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()

        cls.ProductProduct = cls.env["product.product"]
        cls.ResPartner = cls.env["res.partner"]
        cls.Account = cls.env["account.move"]
        cls.AccountLine = cls.env["account.move.line"]

        # Model Data
        cls.product_uom_kg = cls.env.ref("uom.product_uom_kgm")
        cls.product_double_uom = cls.env.ref("uom.product_uom_unit")

        # Partner
        cls.partner = cls.ResPartner.create(
            {
                "name": "Test Partner",
            }
        )

        # DU Product and Simple Product
        cls.du_product = cls.ProductProduct.create(
            {
                "name": "DU",
                "is_double_uom": True,
                "type": "consu",
                "is_storable": True,
                "invoice_policy": "delivery",
                "tracking": "lot",
                "coefficient": 2,
                "uom_id": cls.product_uom_kg.id,
                "double_uom_id": cls.product_double_uom.id,
                "uom_po_id": cls.product_uom_kg.id,
            }
        )
        cls.simple_product = cls.ProductProduct.create(
            {
                "name": "Simple Product",
                "type": "consu",
                "is_storable": True,
                "invoice_policy": "delivery",
                "tracking": "lot",
                "uom_id": cls.product_double_uom.id,
                "uom_po_id": cls.product_double_uom.id,
                "barcode": "*************",
            }
        )

        cls.account_move = cls.Account.create(
            {
                "move_type": "out_invoice",
                "line_ids": [
                    Command.create(
                        {
                            "name": "Product Line",
                            "account_id": cls.env["account.account"]
                            .search([], limit=1)
                            .id,
                            "product_id": cls.du_product.id,
                            "display_type": "product",
                            "qty_double_uom": 10.0,
                            "debit": 100.0,
                            "credit": 0.0,
                        }
                    ),
                ],
            }
        )
