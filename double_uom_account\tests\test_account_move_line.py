from odoo.addons.double_uom_account.tests.common import DoubleUomAccountCommon


class TestAccountMoveLine(DoubleUomAccountCommon):
    def setUp(self):
        super().setUp()
        self.account_move_line = self.env["account.move.line"].create(
            {
                "move_id": self.account_move.id,
                "product_id": self.du_product.id,
                "move_type": "out_invoice",
                "display_type": "product",
            }
        )

    def test_default_qty_double_uom(self):
        """Test default value for qty_double_uom."""
        self.assertEqual(self.account_move_line.qty_double_uom, 0.0)

    def test_compute_has_different_uom_categ(self):
        """Test has_different_uom_categ computation."""
        self.account_move_line._compute_has_different_uom_categ()
        self.assertTrue(self.account_move_line.has_different_uom_categ)

    def test_onchange_quantity(self):
        """Test onchange for quantity."""
        self.account_move_line.quantity = 10.0
        self.account_move_line._onchange_quantity()
        self.assertEqual(self.account_move_line.qty_double_uom, 5.0)

    def test_quantity_default_on_product_select(self):
        """Test default quantity when a product is selected."""

        self.account_move_line._compute_quantity()
        self.assertEqual(self.account_move_line.quantity, 2.0)

    def test_edge_case_zero_coefficient(self):
        """Test edge case where coefficient is zero."""
        self.account_move_line.coefficient = 0
        self.account_move_line._compute_du_coefficient()
        self.account_move_line._onchange_qty_double_uom()
        self.assertEqual(self.account_move_line.qty_double_uom, 0.0)
        self.account_move_line._onchange_quantity()
        self.assertEqual(self.account_move_line.quantity, 0.0)

    def test_negative_quantity(self):
        """Test behavior with negative quantities."""
        self.account_move_line.qty_double_uom = -5
        self.account_move_line._onchange_qty_double_uom()
        self.assertEqual(self.account_move_line.quantity, -10.0)

    def test_in_invoice_uom_behavior(self):
        """Test behavior for in_invoice move type with double UoM."""
        self.account_move_line.quantity = 20
        self.account_move_line._onchange_quantity()
        self.assertEqual(self.account_move_line.qty_double_uom, 10.0)
