<?xml version="1.0" ?>
<odoo>
    <template
        id="report_invoice_document_inherit_double_uom_account"
        inherit_id="account.report_invoice_document"
    >
        <xpath expr="//thead//th[@name='th_quantity']" position="after">
            <t
                t-set="display_du"
                t-value="o.invoice_line_ids.filtered(lambda l: l.product_id.is_double_uom)"
            />
            <t t-if="display_du">
                <th name="th_qty_double_uom" class="text-center">Q.D.U</th>
            </t>
        </xpath>
        <xpath expr="//tbody//td[@name='td_quantity']" position="after">
            <t t-if="display_du">
                <td name="td_qty_double_uom" class="text-center">
                    <span t-field="line.qty_double_uom" />
                    <span t-field="line.double_uom_id" />
                </td>
            </t>
        </xpath>
    </template>
</odoo>
