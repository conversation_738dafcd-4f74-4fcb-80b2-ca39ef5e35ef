from odoo.addons.double_uom_account.tests.common import DoubleUomAccountCommon


class TestAccountMove(DoubleUomAccountCommon):
    def test_action_switch_move_type_positive_amount(self):
        """Test action_switch_move_type with a positive total amount."""
        self.account_move.amount_total = 100.0
        self.account_move.action_switch_move_type()

        for line in self.account_move.line_ids:
            if line.display_type == "product":
                self.assertEqual(line.qty_double_uom, 10.0)

    def test_action_switch_move_type_negative_amount(self):
        """Test action_switch_move_type with a negative total amount."""
        self.account_move.amount_total = -50.0
        self.account_move.action_switch_move_type()

        for line in self.account_move.line_ids:
            if line.display_type == "product":
                self.assertEqual(line.qty_double_uom, -10.0)
